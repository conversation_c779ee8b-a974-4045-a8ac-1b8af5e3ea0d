import { Router } from 'express';
import { secure } from '../../middleware/auth';
import { innovationIdeaControllers } from '../../controllers/innovation/innovationIdea.controller';

export const innovationRoute = Router();

innovationRoute.post(
  '/new-idea',
  secure,
  innovationIdeaControllers.CreateInnovationIdeaHandler
);

innovationRoute.get(
  '/idea-categories',
  secure,
  innovationIdeaControllers.GetAllCategoriesHandler
);

innovationRoute.get(
  '/idea-tags',
  secure,
  innovationIdeaControllers.GetAllTagsHandler
);

innovationRoute.get(
  '/stats',
  secure,
  innovationIdeaControllers.GetInnovationIdeasStats
);

innovationRoute.get(
  '/idea/list',
  secure,
  innovationIdeaControllers.GetAllInnovationIdeasHandler
);

innovationRoute.put(
  '/idea/update',
  secure,
  innovationIdeaControllers.UpdateInnovationIdeaHandler
);

innovationRoute.delete(
  '/idea/:ideaId',
  secure,
  innovationIdeaControllers.DeleteInnovationIdeaHandler
);

innovationRoute.post(
  '/idea/:ideaId/like',
  secure,
  innovationIdeaControllers.ToggleLikeHandler
);

innovationRoute.post(
  '/idea/comment',
  secure,
  innovationIdeaControllers.CreateCommentHandler
);

innovationRoute.delete(
  '/comments/:commentId',
  secure,
  innovationIdeaControllers.DeleteCommentHandler
);
